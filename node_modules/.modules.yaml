hoistPattern:
  - '*'
hoistedDependencies:
  '@electron/get@2.0.3':
    '@electron/get': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/cors@2.8.18':
    '@types/cors': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  accepts@1.3.8:
    accepts: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  base64id@2.0.0:
    base64id: private
  boolean@3.2.0:
    boolean: private
  browserslist@4.25.0:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.3:
    clone-response: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  cookie@0.7.2:
    cookie: private
  cors@2.8.5:
    cors: private
  debug@4.3.7:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  detect-node@2.1.0:
    detect-node: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.4:
    end-of-stream: private
  engine.io-client@6.6.3:
    engine.io-client: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.4:
    engine.io: private
  env-paths@2.2.1:
    env-paths: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es6-error@4.1.1:
    es6-error: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  extract-zip@2.0.1:
    extract-zip: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@8.1.0:
    fs-extra: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-stream@5.2.0:
    get-stream: private
  global-agent@3.0.0:
    global-agent: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  json-buffer@3.0.1:
    json-buffer: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  jsonfile@4.0.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  lodash@4.17.21:
    lodash: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  matcher@3.0.0:
    matcher: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-response@3.1.0:
    mimic-response: private
  ms@2.1.3:
    ms: private
  nan@2.22.2:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  negotiator@0.6.3:
    negotiator: private
  node-releases@2.0.19:
    node-releases: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@6.1.0:
    normalize-url: private
  object-assign@4.1.1:
    object-assign: private
  object-keys@1.1.1:
    object-keys: private
  once@1.4.0:
    once: private
  p-cancelable@2.1.1:
    p-cancelable: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  progress@2.0.3:
    progress: private
  pump@3.0.2:
    pump: private
  quick-lru@5.1.1:
    quick-lru: private
  require-directory@2.1.1:
    require-directory: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  responselike@2.0.1:
    responselike: private
  roarr@2.15.4:
    roarr: private
  rxjs@7.8.2:
    rxjs: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@6.3.1:
    semver: private
  serialize-error@7.0.1:
    serialize-error: private
  shell-quote@1.8.2:
    shell-quote: private
  socket.io-adapter@2.5.5:
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  source-map-js@1.2.1:
    source-map-js: private
  sprintf-js@1.1.3:
    sprintf-js: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  sumchecker@3.0.1:
    sumchecker: private
  supports-color@8.1.1:
    supports-color: private
  tree-kill@1.2.2:
    tree-kill: private
  tslib@2.8.1:
    tslib: private
  type-fest@0.13.1:
    type-fest: private
  undici-types@6.21.0:
    undici-types: private
  universalify@0.1.2:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  vary@1.1.2:
    vary: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.17.1:
    ws: private
  xmlhttprequest-ssl@2.1.2:
    xmlhttprequest-ssl: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
ignoredBuilds:
  - electron
  - node-pty
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.5
pendingBuilds: []
prunedAt: Thu, 29 May 2025 11:15:37 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
