#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/electron@36.3.2/node_modules/electron/node_modules:/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/electron@36.3.2/node_modules:/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/electron@36.3.2/node_modules/electron/node_modules:/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/electron@36.3.2/node_modules:/home/<USER>/projects/ai-terminal-autopilot/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../electron@36.3.2/node_modules/electron/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../electron@36.3.2/node_modules/electron/cli.js" "$@"
fi
