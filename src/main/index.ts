import { app, BrowserWindow } from 'electron';
import path from 'path';
import { createServer } from 'http';
import { Server } from 'socket.io';
import pty from 'node-pty';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // and load the index.html of the app.
  mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  // Open the DevTools
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', () => {
  // Setup socket.io server
  const httpServer = createServer();
  const io = new Server(httpServer, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    }
  });

  httpServer.listen(3001, () => {
    console.log('Socket.IO server running on port 3001');
  });

  // Create terminal processes
  const shell = process.env.SHELL || '/bin/bash';
  const terminals = new Map<string, pty.IPty>();

  io.on('connection', (socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Create a new terminal session
    const term = pty.spawn(shell, [], {
      name: 'xterm-256color',
      cols: 80,
      rows: 24,
      cwd: process.cwd(),
      env: process.env,
    });

    terminals.set(socket.id, term);

    // Handle terminal input
    socket.on('terminal-input', (data: { input: string }) => {
      term.write(data.input);
    });

    // Handle terminal output
    term.onData((data) => {
      socket.emit('terminal-output', { output: data });
    });

    // Handle terminal resize
    socket.on('terminal-resize', (data: { cols: number; rows: number }) => {
      term.resize(data.cols, data.rows);
    });

    // Clean up on disconnect
    socket.on('disconnect', () => {
      term.kill();
      terminals.delete(socket.id);
    });
  });

  createWindow();
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});