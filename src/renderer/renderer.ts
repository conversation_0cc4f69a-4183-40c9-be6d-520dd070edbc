import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { io, Socket } from 'socket.io-client';
import { create } from 'zustand';

// Define state structure
interface TerminalState {
  currentMode: 'terminal' | 'chat' | 'agent';
  activeSessionId: string | null;
  sessions: Array<{ id: string; title: string }>;
  workingDirectory: string;
  setCurrentMode: (mode: 'terminal' | 'chat' | 'agent') => void;
  setActiveSession: (sessionId: string) => void;
}

// Create Zustand store
const useStore = create<TerminalState>((set) => ({
  currentMode: 'terminal',
  activeSessionId: null,
  sessions: [],
  workingDirectory: process.cwd(),
  setCurrentMode: (mode) => set({ currentMode: mode }),
  setActiveSession: (sessionId) => set({ activeSessionId: sessionId }),
}));

// Initialize terminal
const term = new Terminal({
  cursorBlink: true,
  fontSize: 14,
  fontFamily: 'Menlo, Monaco, "Courier New", monospace',
  theme: {
    background: '#0f172a',
    foreground: '#f1f5f9',
    cursor: '#f1f5f9',
  },
});

const fitAddon = new FitAddon();
term.loadAddon(fitAddon);
term.loadAddon(new WebLinksAddon());

const terminalContainer = document.getElementById('terminal');
if (terminalContainer) {
  term.open(terminalContainer);
  fitAddon.fit();
}

// Connect to socket.io server
const socket: Socket = io('http://localhost:3001');

// Handle terminal output from server
socket.on('terminal-output', (data: { output: string }) => {
  term.write(data.output);
});

// Send user input to server
term.onData((data) => {
  socket.emit('terminal-input', { input: data });
});

// Handle terminal resize
window.addEventListener('resize', () => {
  fitAddon.fit();
  socket.emit('terminal-resize', { 
    cols: term.cols, 
    rows: term.rows 
  });
});

// Initialize terminal size
socket.emit('terminal-resize', { 
  cols: term.cols, 
  rows: term.rows 
});

// Handle mode switching
document.querySelectorAll('.mode-btn').forEach(button => {
  button.addEventListener('click', () => {
    const mode = button.getAttribute('data-mode');
    if (mode === 'terminal' || mode === 'chat' || mode === 'agent') {
      useStore.getState().setCurrentMode(mode);
      
      // Update UI
      document.querySelectorAll('.mode-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      button.classList.add('active');
    }
  });
});

// Focus terminal when clicking anywhere in the container
terminalContainer?.addEventListener('click', () => {
  term.focus();
});

term.focus();