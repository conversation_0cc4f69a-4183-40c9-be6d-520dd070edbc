{"name": "warp-terminal-clone", "version": "1.0.0", "description": "A Warp Terminal clone built with Electron, TypeScript, and xterm.js", "main": "dist/main/index.js", "scripts": {"build": "tsc -p tsconfig.json && tsc -p tsconfig.renderer.json && tailwindcss -i src/renderer/styles.css -o dist/renderer/styles.css", "start": "electron .", "dev": "concurrently \"tsc -p tsconfig.json -w\" \"tsc -p tsconfig.renderer.json -w\" \"tailwindcss -i src/renderer/styles.css -o dist/renderer/styles.css --watch\" \"wait-on dist/main/index.js && electron .\""}, "keywords": ["terminal", "electron", "xterm"], "author": "Your Name", "license": "MIT", "dependencies": {"@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/electron": "^1.6.12", "@types/node": "^22.15.24", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "electron": "^36.3.2", "node-pty": "^1.0.0", "postcss": "^8.5.4", "socket.io": "^4.8.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "wait-on": "^7.2.0"}}